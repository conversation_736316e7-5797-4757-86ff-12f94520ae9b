import { Divider } from "@/components/ui/divider";
import { type FormField } from "@/types/form.types";
import { useCallback } from "react";
import { FieldActions } from "./field-actions";
import { FieldDescriptionInput } from "./field-description-input";
import { FieldLabelInput } from "./field-label-input";

interface EditModeProps {
  field: FormField;
  onUpdate: (property: keyof FormField, value: unknown) => void;
  onDuplicate: () => void;
  onDelete: () => void;
  fieldIcon?: React.ReactNode;
  fieldTypeName?: string;
  labelPlaceholder?: string;
  descriptionPlaceholder?: string;
  descriptionMultiline?: boolean;
  showRequiredToggle?: boolean;
  showDescriptionToggle?: boolean;
  children?: React.ReactNode;
}

/**
 * Shared editing mode component for form field elements.
 * Handles the common editing UI pattern with label, description, and actions.
 */
export function EditMode({
  field,
  onUpdate,
  onDuplicate,
  onDelete,
  fieldIcon,
  fieldTypeName,
  labelPlaceholder,
  descriptionPlaceholder,
  descriptionMultiline = false,
  showRequiredToggle = true,
  showDescriptionToggle = true,
  children,
}: EditModeProps) {
  // Memoize toggle functions to prevent infinite re-renders
  const handleToggleDescription = useCallback(() => {
    onUpdate("showDescription", !field.showDescription);
  }, [onUpdate, field.showDescription]);

  const handleToggleRequired = useCallback(() => {
    onUpdate("required", !field.required);
  }, [onUpdate, field.required]);

  return (
    <div className="space-y-4">
      <div className="space-y-3">
        {/* Label input */}
        <FieldLabelInput
          value={field.label}
          onChange={(value) => onUpdate("label", value)}
          placeholder={labelPlaceholder}
        />

        {/* Description input (conditional) */}
        {field.showDescription && (
          <FieldDescriptionInput
            value={field.description}
            onChange={(value) => onUpdate("description", value)}
            placeholder={descriptionPlaceholder}
            multiline={descriptionMultiline}
          />
        )}

        {/* Field-specific content */}
        {children}
      </div>

      <Divider />

      {/* Actions and toggles */}
      <FieldActions
        onDuplicate={onDuplicate}
        onDelete={onDelete}
        fieldIcon={fieldIcon}
        fieldTypeName={fieldTypeName}
        showDescription={field.showDescription}
        onToggleDescription={
          showDescriptionToggle ? handleToggleDescription : undefined
        }
        required={field.required}
        onToggleRequired={showRequiredToggle ? handleToggleRequired : undefined}
      />
    </div>
  );
}
